<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Database\Seeders\ExistingReservationsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(ExistingReservationsSeeder::class)]
class ExistingReservationsSeederTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create required dependencies
        $this->createTestDependencies();
    }

    #[Test]
    public function it_adjusts_reservation_dates_to_start_from_today()
    {
        // Run the seeder (it automatically clears existing reservations)
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->assertExitCode(0);

        // Verify exactly 11 reservations were created (as defined in seeder data)
        $this->assertEquals(11, Reservation::count());

        // Get the earliest and latest reservations
        $earliestReservation = Reservation::orderBy('booking_date')->first();
        $latestReservation = Reservation::orderBy('booking_date', 'desc')->first();

        // Verify the original 10-day range is preserved
        $daysDifference = $earliestReservation->booking_date->diffInDays($latestReservation->booking_date);
        $this->assertEquals(10, $daysDifference);

        // Verify dates are adjusted correctly - should start from today
        // The seeder moves all dates forward so the earliest starts from today
        // while preserving the original 10-day range
        $this->assertEquals(now()->toDateString(), $earliestReservation->booking_date->toDateString());
        $this->assertEquals(now()->addDays(10)->toDateString(), $latestReservation->booking_date->toDateString());

        // Verify no reservations are scheduled in the past
        $reservationsInPast = Reservation::where('booking_date', '<', now()->toDateString())->count();
        $this->assertEquals(0, $reservationsInPast, 'No reservations should be scheduled in the past');
    }

    #[Test]
    public function it_preserves_reservation_relationships()
    {
        // Run the seeder (it automatically clears existing reservations)
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->assertExitCode(0);

        // Verify all reservations have proper field and user relationships
        $reservations = Reservation::with(['field', 'user'])->get();
        $this->assertGreaterThan(0, $reservations->count());

        foreach ($reservations as $reservation) {
            $this->assertNotNull($reservation->field);
            $this->assertNotNull($reservation->user);
            $this->assertEquals('<EMAIL>', $reservation->user->email);
        }

        // Get a reservation with utilities (seeder data includes several with utilities)
        $reservationWithUtilities = Reservation::with(['field', 'user', 'utilities'])
            ->whereHas('utilities')
            ->first();

        $this->assertNotNull($reservationWithUtilities, 'Should have at least one reservation with utilities');

        // Verify utility relationships and pivot data are preserved
        $this->assertGreaterThan(0, $reservationWithUtilities->utilities->count());

        $utility = $reservationWithUtilities->utilities->first();
        $this->assertNotNull($utility->pivot->hours);
        $this->assertNotNull($utility->pivot->rate);
        $this->assertNotNull($utility->pivot->cost);
    }

    #[Test]
    public function it_always_clears_and_recreates_reservations()
    {
        // Create some initial test reservations
        Reservation::factory()->count(3)->create();
        $this->assertEquals(3, Reservation::count());

        // Run the seeder (it always clears existing reservations first)
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->assertExitCode(0);

        // Should have exactly 11 reservations (the seeder data count), not 3 + 11
        $this->assertEquals(11, Reservation::count());

        // Run the seeder again
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->assertExitCode(0);

        // Should still have exactly 11 reservations (cleared and recreated)
        $this->assertEquals(11, Reservation::count());
    }

    #[Test]
    public function it_creates_reservations_with_correct_data_structure()
    {
        // Run the seeder
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->assertExitCode(0);

        // Verify all required fields are populated correctly
        $reservations = Reservation::all();
        $this->assertEquals(11, $reservations->count());

        foreach ($reservations as $reservation) {
            // Verify required fields are not null
            $this->assertNotNull($reservation->field_id);
            $this->assertNotNull($reservation->user_id);
            $this->assertNotNull($reservation->booking_date);
            $this->assertNotNull($reservation->start_time);
            $this->assertNotNull($reservation->end_time);
            $this->assertNotNull($reservation->duration_hours);
            $this->assertNotNull($reservation->total_cost);
            $this->assertNotNull($reservation->status);
            $this->assertNotNull($reservation->customer_name);
            $this->assertNotNull($reservation->customer_email);
            $this->assertNotNull($reservation->customer_phone);

            // Verify status is valid
            $this->assertContains($reservation->status, ['Completed', 'Confirmed']);
        }
    }

    #[Test]
    public function it_handles_missing_dependencies_gracefully()
    {
        // Remove a field that the seeder expects (Veld futbol appears in 4 reservations)
        Field::where('name', 'Veld futbol')->delete();

        // Run the seeder - it should handle missing dependencies gracefully
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->assertExitCode(0);

        // Should create fewer reservations (11 - 4 = 7) since 4 reservations use 'Veld futbol'
        $this->assertEquals(7, Reservation::count());

        // Verify no reservations were created for the missing field
        $reservations = Reservation::with('field')->get();
        foreach ($reservations as $reservation) {
            $this->assertNotEquals('Veld futbol', $reservation->field->name);
        }
    }

    #[Test]
    public function it_creates_utility_attachments_with_correct_pivot_data()
    {
        // Run the seeder
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->assertExitCode(0);

        // Find a specific reservation that should have utilities (first reservation with 'Paña di mesa')
        $reservation = Reservation::with(['utilities', 'field'])
            ->whereHas('field', function ($query) {
                $query->where('name', 'Veld futbol');
            })
            ->whereHas('utilities', function ($query) {
                $query->where('name', 'Paña di mesa');
            })
            ->first();

        $this->assertNotNull($reservation, 'Should find reservation with Paña di mesa utility');

        $utility = $reservation->utilities->where('name', 'Paña di mesa')->first();
        $this->assertNotNull($utility);

        // Verify pivot data matches seeder expectations
        $this->assertEquals(1, $utility->pivot->hours);
        $this->assertEquals(15.00, $utility->pivot->rate);
        $this->assertEquals(15.00, $utility->pivot->cost);
    }

    /**
     * Create the required test dependencies
     */
    private function createTestDependencies(): void
    {
        // Create test user
        User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Andy Janga',
        ]);

        // Create test fields
        $fields = [
            'Veld futbol',
            'Veld Bolas',
            'Veld Multi',
            'Patio Area',
        ];

        foreach ($fields as $fieldName) {
            Field::factory()->create([
                'name' => $fieldName,
                'hourly_rate' => 50.00,
                'night_hourly_rate' => 80.00,
            ]);
        }

        // Create test utilities with rates matching seeder expectations
        $utilities = [
            ['name' => 'Paña di mesa', 'hourly_rate' => 15.00],
            ['name' => 'Sta tafel', 'hourly_rate' => 15.00],
            ['name' => 'Mesa rondo', 'hourly_rate' => 15.00],
            ['name' => 'Stoel', 'hourly_rate' => 1.50], // Special rate for Stoel as used in seeder
        ];

        foreach ($utilities as $utilityData) {
            Utility::factory()->create($utilityData);
        }
    }
}
